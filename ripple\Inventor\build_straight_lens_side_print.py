import win32com.client
import os
import shutil
import time
import numpy as np
# https://damassets.autodesk.net/content/dam/autodesk/www/pdfs/Inventor2022ObjectModel.pdf
# kPositiveExtentDirection: 20993
# kNegativeExtentDirection: 20994
# kSymmetricExtentDirection: 20995
# kJoinOperation: 20481
# kCutOperation: 20482
# kIntersectOperation: 20483

# Assume the input model is already repaired and converted to base model

#==================Lens parameters==================
Part_FileName_BaseIPT = r'D:\WORK\02_Research\10_EDWA_packaging\10_Packaging\04_D143_LGT_debug\EDWA\12_1550_gaussian_25um.ipt'
cut_free_hanging_part = True

# TODO: note the x0 should have the opposite sign if the lens is attached to the right side
# TODO:z_bottom + 0.5 to make sure two parts connect without a gap
lens_pars = {'x0': 0, 'y0': 0, 'z_bottom': 33, 'extra_body_len': 3,
             'x_r': 35.295, 'y_r': 34.451}

# there will be some error when z_bottom is exactly where the lens top stops
rotate_pars = {'theta_x_deg': 0,
               'theta_y_deg': 0,
               'px': 0, 'py': 0, 'pz': 0}   # pivot point
translate_pars = {'tx': 0, 'ty': 0, 'tz': 0}    # translation w.r.t. the source/target field        # FIXME: not used!
cut_pars = {'cut_height': 5, 'cut_angle_deg': -60, 'max_radius': 125./2}   # max_radius only works perpendicular to xy plane centered around (x0, y0) in lens_pars
support_pars = {'in_facet_dist': 3, 'double_illu_offset': 0}

Inventor_pars = [lens_pars, rotate_pars, translate_pars, cut_pars, support_pars]

num_rotations = bool(rotate_pars['theta_x_deg']) + bool(rotate_pars['theta_y_deg'])
assert num_rotations <= 1, NotImplemented("Only support max. one rotation")

if num_rotations == 1:
    rotation_axis = [key.split('_')[1] for key, value in rotate_pars.items() if value != 0 and 'deg' in key][0]
else:
    rotation_axis = 'y'

assert rotate_pars['px'] == 0 and rotate_pars['py'] == 0, NotImplemented('Rotation along none z axis is not implemented.')

invApp = win32com.client.Dispatch("Inventor.Application")
invApp.Visible = True

oPartDoc = invApp.Documents.Open(Part_FileName_BaseIPT)
oPartCompDef = oPartDoc.ComponentDefinition

#==================Set parameters==================
oParams = oPartCompDef.Parameters
userParams = oParams.UserParameters

def set_pars(par_dict):
    for key, value in par_dict.items():
        if value is None:
            continue
        if key.split('_')[-1] == 'deg':
            userParams.AddByExpression(key, f"{value} deg", oPartDoc.UnitsOfMeasure.AngleUnits)
        else:
            userParams.AddByExpression(key, f"{value} mm", oPartDoc.UnitsOfMeasure.LengthUnits)

for pars in Inventor_pars:
    set_pars(pars)

oTransGeom = invApp.TransientGeometry
# ================== Rotate and translate model ==================
oWorkPoint = oPartCompDef.WorkPoints.AddFixed(oTransGeom.CreatePoint(-userParams.Item('x0').Value,
                                                                     -userParams.Item('y0').Value, 0),
                                              Construction=True)
oWorkAxis = oPartCompDef.WorkAxes.AddByNormalToSurface(Surface=oPartCompDef.WorkPlanes.Item("XY Plane"),
                                                       Point=oWorkPoint, Construction=True)

pivotPoint = oPartCompDef.WorkPoints.AddFixed(oTransGeom.CreatePoint(
    -userParams.Item('x0').Value + userParams.Item('px').Value,
    -userParams.Item('y0').Value + userParams.Item('py').Value,
    userParams.Item('pz').Value), Construction=False)

rotateAxis = oPartCompDef.WorkAxes.AddByLineAndPoint(Line=oPartCompDef.WorkAxes.Item(f"{rotation_axis.upper()} Axis"),
                                                     Point=pivotPoint, Construction=True)
WorkPlane = oPartCompDef.WorkPlanes.AddByLinePlaneAndAngle(Line=rotateAxis,
                                                           Plane=oPartCompDef.WorkPlanes.Item("XY Plane"),
                                                           Angle=-userParams.Item(f'theta_{rotation_axis}_deg').Value,
                                                           Construction=True)
chip_facet = oPartCompDef.WorkPlanes.AddByPlaneAndOffset(Plane=WorkPlane, Offset=-userParams.Item('pz').Value,
                                                         Construction=False)

z_axis = oPartCompDef.WorkAxes.AddByNormalToSurface(Surface=chip_facet, Point=pivotPoint, Construction=False)

center_pt = oPartCompDef.WorkPoints.AddByCurveAndEntity(Curve=z_axis, Entity=chip_facet, Construction=False)

# ================== Build lens body ==================
lens_bottom = oPartCompDef.WorkPlanes.AddByPlaneAndOffset(Plane=oPartCompDef.WorkPlanes.Item("XY Plane"),
                                                          Offset=userParams.Item('z_bottom').Value,
                                                          Construction=True)
oSketch = oPartCompDef.Sketches.Add(lens_bottom)
ellipse = oSketch.SketchEllipses.Add(CenterPoint=oTransGeom.CreatePoint2d(0, 0),
                                     MajorAxisVector=oTransGeom.CreateUnitVector2d(1, 0),
                                     MajorRadius=userParams.Item('x_r').Value,
                                     MinorRadius=userParams.Item('y_r').Value)

oProfile = oSketch.Profiles.AddForSolid()
lens_body = oPartCompDef.Features.ExtrudeFeatures.CreateExtrudeDefinition(oProfile, 20481)
lens_body.SetDistanceExtent(userParams.Item('z_bottom').Value + userParams.Item('extra_body_len').Value, 20994)
lensBodyExtrude = oPartCompDef.Features.ExtrudeFeatures.Add(lens_body)

# ================== Cut top ==================
if cut_free_hanging_part:
    chip_surf = oPartCompDef.WorkPlanes.AddByPlaneAndOffset(Plane=oPartCompDef.WorkPlanes.Item("XZ Plane"),
                                                            Offset=userParams.Item('cut_height').Value, Construction=True)
    cut_height = oPartCompDef.WorkAxes.AddByTwoPlanes(chip_surf, chip_facet, Construction=True)
    top_cut_plane = oPartCompDef.WorkPlanes.AddByLinePlaneAndAngle(Line=cut_height,
                                                                   Plane=oPartCompDef.WorkPlanes.Item("XZ Plane"),
                                                                   Angle=userParams.Item('cut_angle_deg').Value,
                                                                   Construction=True)
    # cut tilt
    top_cut_sketch = oPartCompDef.Sketches.Add(top_cut_plane)
    top_cut_contour = top_cut_sketch.SketchLines.AddAsTwoPointRectangle(oTransGeom.CreatePoint2d(-50, 0),
                                                                        oTransGeom.CreatePoint2d(50, -50))
    top_cut_profile = top_cut_sketch.Profiles.AddForSolid()
    top_cut = oPartCompDef.Features.ExtrudeFeatures.CreateExtrudeDefinition(top_cut_profile, 20482)
    top_cut.SetDistanceExtent(20, 20993)
    top_cut_extrude = oPartCompDef.Features.ExtrudeFeatures.Add(top_cut)

    # cut remainder
    top_rem_sketch = oPartCompDef.Sketches.Add(chip_facet)
    cut_rem_contour = top_rem_sketch.SketchLines.AddAsTwoPointRectangle(
        oTransGeom.CreatePoint2d(userParams.Item('cut_height').Value, 20), oTransGeom.CreatePoint2d(50, -50))

    cut_rem_profile = top_rem_sketch.Profiles.AddForSolid()
    rem_cut = oPartCompDef.Features.ExtrudeFeatures.CreateExtrudeDefinition(cut_rem_profile, 20482)
    rem_cut.SetDistanceExtent(20, 20994)
    cut_rem_extrude = oPartCompDef.Features.ExtrudeFeatures.Add(rem_cut)

if cut_pars['max_radius'] is not None:
    max_radius_sketch = oPartCompDef.Sketches.Add(chip_facet)

    # r_center_pt = oTransGeom.CreatePoint2d(userParams.Item('y0').Value, userParams.Item('x0').Value)
    r_center_pt = oTransGeom.CreatePoint2d(0, 0)
    max_r_circle = max_radius_sketch.SketchCircles.AddByCenterRadius(CenterPoint=r_center_pt,
                                                                     Radius=userParams.Item('max_radius').Value)
    max_r_profile = max_radius_sketch.Profiles.AddForSolid()
    max_r_extrude = oPartCompDef.Features.ExtrudeFeatures.CreateExtrudeDefinition(max_r_profile, 20483)
    max_r_extrude.SetDistanceExtent(100, 20995)
    max_r_intersect = oPartCompDef.Features.ExtrudeFeatures.Add(max_r_extrude)

# ================== Cut off extra structure ==================
if num_rotations > 0:   # not necessary when rotation is not applied
    end_facet = oPartCompDef.WorkPlanes.AddByPlaneAndOffset(Plane=chip_facet, Offset=-userParams.Item('in_facet_dist').Value,
                                                            Construction=True)
    trim_sketch = oPartCompDef.Sketches.Add(end_facet)
    trim_contour = trim_sketch.SketchCircles.AddByCenterRadius(CenterPoint=oTransGeom.CreatePoint2d(0, 0), Radius=50)
    trim_profile = trim_sketch.Profiles.AddForSolid()
    trim_def = oPartCompDef.Features.ExtrudeFeatures.CreateExtrudeDefinition(trim_profile, 20482)
    trim_def.SetDistanceExtent(20, 20994)
    trim_extrude = oPartCompDef.Features.ExtrudeFeatures.Add(trim_def)

oPartDoc.Update()
while True:
    if_save = input("Do you want to save the result? (y/n) ")
    if if_save == "y":
        oPartDoc.Save()
        break
    elif if_save == "n":
        invApp.Quit()
        exit()

# ================== Cut into two parts ==================
cut_model = input("Do you want to cut the model for double illumination? (y/n) ")
if cut_model == "n":
    invApp.Quit()
    exit()
elif cut_model == "y":
    split_sketch = oPartCompDef.Sketches.Add(end_facet)
    split_contour = split_sketch.SketchCircles.AddByCenterRadius(CenterPoint=oTransGeom.CreatePoint2d(0, 0),
                                                                 Radius=10)
    split_profile = split_sketch.Profiles.AddForSolid()
    split_def = oPartCompDef.Features.ExtrudeFeatures.CreateExtrudeDefinition(split_profile, 20482)
    split_def.SetDistanceExtent(
        userParams.Item('in_facet_dist').Value + userParams.Item('double_illu_offset').Value,
        20993)
    split_extrude = oPartCompDef.Features.ExtrudeFeatures.Add(split_def)
    extrude_name = split_extrude.Name

    oPartDoc.Update()
    oPartDoc.Save()
    invApp.Quit()

    parent_dir = os.path.dirname(Part_FileName_BaseIPT)
    model_name = os.path.basename(Part_FileName_BaseIPT)

    base_model_name = model_name.split('.')[0] + '_double_illumination.ipt'
    base_model_path = os.path.join(parent_dir, base_model_name)

    shutil.copyfile(Part_FileName_BaseIPT, base_model_path)

    time.sleep(10)  # to avoid failing opening the other ipt file
    # ================== Modify the bottom part ==================
    print("Cutting base model...")
    invApp = win32com.client.Dispatch("Inventor.Application")
    invApp.Visible = True

    oPartDoc = invApp.Documents.Open(base_model_path)
    oPartCompDef = oPartDoc.ComponentDefinition
    oParams = oPartCompDef.Parameters
    userParams = oParams.UserParameters

    features = oPartCompDef.Features.ExtrudeFeatures

    extrusion = features.Item(extrude_name)
    extrusion.Operation = 20483
    extrusion.Definition.Extent.Direction = 20993
    extrusion.Definition.Extent.Distance.Value = (
            userParams.Item('in_facet_dist').Value + userParams.Item('double_illu_offset').Value)

    oPartDoc.Update()
    oPartDoc.Save()
    invApp.Quit()
